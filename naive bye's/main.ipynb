#%%
from sklearn.datasets import load_iris
X, y = load_iris(return_X_y=True)
#%%
X
#%%
y
#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
#%%
from sklearn.naive_bayes import GaussianNB
gnb = GaussianNB()
#%%
gnb.fit(X_train, y_train)

#%%
y_pred = gnb.predict(X_test)
#%%
from sklearn.metrics import accuracy_score
accuracy_score(y_test, y_pred)
#%%
from sklearn.metrics import confusion_matrix
cm = confusion_matrix(y_test, y_pred)
cm