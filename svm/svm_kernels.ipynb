#%%
import  numpy as np
import matplotlib.pyplot as plt

x = np.linspace(-5.0, 5.0, 100)
y = np.sqrt(10**2 - x**2)

y = np.hstack((y, -y))
x = np.hstack((x, -x))

#%%
x1 = np.linspace(-5.0, 5.0, 100)
y1 = np.sqrt(5**2 - x1**2)
y1 = np.hstack((y1, -y1))
x1 = np.hstack((x1, -x1))

#%%
plt.scatter(y, x, color="red")
plt.scatter(y1, x1, color="blue")

#%%
import pandas as pd
df1 = pd.DataFrame(np.vstack((x, y)).T, columns=["x1", "x2"])
df1["Y"] = 0
df2 = pd.DataFrame(np.vstack((x1, y1)).T, columns=["x1", "x2"])
df2["Y"] = 1
df = pd.concat([df1, df2])
df.tail()

#%%
df["x1_square"] = df['x1']**2
df["x2_square"] = df['x2']**2
df["x1x2"] = (df["x1"] * df["x2"])
df.head()

#%%
X = df[["x1", "x2", "x1_square", "x2_square", "x1x2"]]
y = df['Y']
#%%
import plotly.express as px
fig = px.scatter_3d(df, x="x1_square", y="x2_square", z="x1x2", color="Y")
fig.show()

#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=123)

#%%
from sklearn.svm import SVC
clf = SVC(kernel="linear")
clf.fit(X_train, y_train)
y_pred = clf.predict(X_test)
from sklearn.metrics import confusion_matrix
cm = confusion_matrix(y_test, y_pred)
#%%
cm