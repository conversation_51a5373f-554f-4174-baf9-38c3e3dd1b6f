#%%
import seaborn as sns
df = sns.load_dataset("tips")
df.head()
#%%
X = df[["tip", "sex", "smoker", "day", "time", "size"]]
y = df["total_bill"]
#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.25, random_state=42)
#%%
X_train
#%%
from sklearn.preprocessing import LabelEncoder
le1 = LabelEncoder()
le2 = LabelEncoder()
le3 = LabelEncoder()
#%%
X_train['sex'] = le1.fit_transform(X_train["sex"])
X_train["smoker"] = le2.fit_transform(X_train["smoker"])
X_train["time"] = le3.fit_transform(X_train["time"])
#%%
X_train
#%%
X_test['sex'] = le1.transform(X_test["sex"])
X_test["smoker"] = le2.transform(X_test["smoker"])
X_test["time"] = le3.transform(X_test["time"])
#%%
X_test

#%%
# One Hot Encoding
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import OneHotEncoder
ct = ColumnTransformer(transformers=[('onehot', OneHotEncoder(drop="first"), [3])], remainder='passthrough')
#%%
X_train = ct.fit_transform(X_train)
X_test = ct.transform(X_test)
#%%
X_train
#%%
X_test

#%%
from sklearn.svm import SVR
model = SVR()
#%%
model.fit(X_train, y_train)
y_pred = model.predict(X_test)

#%%
from sklearn.metrics import mean_squared_error
mean_squared_error(y_test, y_pred)
#%%
from sklearn.metrics import mean_absolute_error
mean_absolute_error(y_test, y_pred)

#%%
from sklearn.metrics import r2_score
r2_score(y_test, y_pred)
