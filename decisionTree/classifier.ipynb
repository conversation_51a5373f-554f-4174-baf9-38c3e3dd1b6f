#%%
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
%matplotlib inline
#%%
from sklearn.datasets import load_iris
iris = load_iris()
#%%
iris
#%%
X = pd.DataFrame(iris.data, columns=iris.feature_names)
X
#%%
y = pd.DataFrame(iris.target, columns=['target'])
y
#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
#%%
from sklearn.tree import DecisionTreeClassifier
classifier = DecisionTreeClassifier()
classifier.fit(X_train, y_train)
#%%
from sklearn import tree
plt.figure(figsize=(15, 10))
tree.plot_tree(classifier, filled=True)
plt.show()
#%%
y_pred = classifier.predict(X_test)
#%%
from sklearn.metrics import accuracy_score
accuracy_score(y_test, y_pred)
#%%
from sklearn.metrics import confusion_matrix
confusion_matrix(y_test, y_pred)
#%% md
DECISION TREE WITH HYPERPARAMETER TUNNING
#%%
params = {
    'criterion': ['gini', 'entropy'],
    'max_depth': [2, 4, 6, 8, 10, 12],
    'min_samples_split': [2, 5, 10],
    'min_samples_leaf': [1, 2, 4]
}