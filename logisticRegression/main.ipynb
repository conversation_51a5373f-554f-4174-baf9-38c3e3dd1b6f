#%%
import pandas as pd

#%%
from sklearn.datasets import make_classification

X, y = make_classification(n_samples=1000, n_features=10, n_classes=2, random_state=42)

#%%
pd.DataFrame(X, columns=["x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10"])
#%%
pd.DataFrame(y, columns=['target'])

#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

#%%
from sklearn.linear_model import LogisticRegression
lr = LogisticRegression()
lr.fit(X_train, y_train)
#%%
lr.coef_
#%%
lr.intercept_
#%%
y_pred = lr.predict(X_test)
y_pred
#%%
probas = lr.predict_proba(X_test)
probas

#%%
from sklearn.metrics import confusion_matrix, classification_report
cm = confusion_matrix(y_test, y_pred)
cm