#%%
from sklearn.linear_model import LogisticRegression

#%%
model = LogisticRegression(max_iter=1000)
c_values = [100, 10, 1.0, 0.1, 0.01]
params = [
    {'solver': ['liblinear', 'saga'], 'penalty': ['l1'], 'C': c_values},
    {'solver': ['lbfgs', 'liblinear', 'newton-cg', 'sag', 'saga'], 'penalty': ['l2'], 'C': c_values},
    {'solver': ['saga'], 'penalty': ['elasticnet'], 'C': c_values, 'l1_ratio': [0.5]}
]
#%%
from sklearn.model_selection import GridSearchCV, StratifiedKFold
cv = StratifiedKFold(n_splits=5)
grid = GridSearchCV(estimator=model, param_grid=params, cv=cv, scoring='accuracy', n_jobs=-1)

#%%
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler

X, y = make_classification(n_samples=1000, n_features=10, n_classes=2, random_state=42)
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

grid.fit(X_train_scaled, y_train)
#%%
grid.best_params_
#%%
y_pred = grid.predict(X_test_scaled)
#%%
from sklearn.metrics import accuracy_score
accuracy_score(y_test, y_pred)

#%%
from sklearn.metrics import confusion_matrix
cm = confusion_matrix(y_test, y_pred)
cm
#%%
from sklearn.metrics import classification_report
print(classification_report(y_test, y_pred))

#%% md
Random SearchCV
#%%
model = LogisticRegression()
from sklearn.model_selection import RandomizedSearchCV
randomcv = RandomizedSearchCV(estimator=model, param_distributions=params, cv=5, scoring='accuracy')
#%%
randomcv.fit(X_train_scaled, y_train)
#%%
randomcv.best_score_

#%%
randomcv.best_params_

#%%
y_pred = randomcv.predict(X_test_scaled)

#%%
score = accuracy_score(y_test, y_pred)
score