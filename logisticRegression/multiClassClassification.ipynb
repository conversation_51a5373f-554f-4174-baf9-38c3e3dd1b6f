#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
%matplotlib inline
#%%
from sklearn.datasets import make_classification
X, y = make_classification(n_samples=1000, n_features=10, n_informative=6, n_classes=3, random_state=42)

#%%
pd.DataFrame(X)
#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

#%%
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

lr = LogisticRegression()
lr.fit(X_train_scaled, y_train)
y_pred = lr.predict(X_test_scaled)
#%%
from sklearn.metrics import accuracy_score
accuracy_score(y_test, y_pred)

#%%
from sklearn.metrics import confusion_matrix
confusion_matrix(y_test, y_pred)