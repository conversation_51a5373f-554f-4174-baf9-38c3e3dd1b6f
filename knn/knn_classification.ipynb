#%%
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
%matplotlib inline
#%%
from sklearn.datasets import make_classification
X, y = make_classification(n_samples=1000, n_features=3, n_redundant=0, n_classes=2, random_state=42)
#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
#%%
sns.scatterplot(x=pd.DataFrame(X)[0], y=pd.DataFrame(X)[1], hue=y)
#%%
from sklearn.neighbors import KNeighborsClassifier
model = KNeighborsClassifier(n_neighbors=5)
model.fit(X_train, y_train)

#%%
y_pred = model.predict(X_test)
#%%
from sklearn.metrics import accuracy_score
accuracy_score(y_test, y_pred)
