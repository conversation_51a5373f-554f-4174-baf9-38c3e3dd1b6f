#%%
from sklearn.datasets import make_regression
X, y = make_regression(n_samples=1000, n_features=2, noise=10, random_state=42)
#%%
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
#%%
from sklearn.neighbors import KNeighborsRegressor
model = KNeighborsRegressor(n_neighbors=5)
model.fit(X_train, y_train)
#%%
y_pred = model.predict(X_test)
#%%
from sklearn.metrics import mean_squared_error
mean_squared_error(y_test, y_pred)
#%%
from sklearn.metrics import mean_absolute_error
mean_absolute_error(y_test, y_pred)
#%%
from sklearn.metrics import r2_score
r2_score(y_test, y_pred)
